{"name": "tiktok-tools", "version": "1.0.0", "description": "TikTok直播监控工具", "main": "main.js", "bin": "main.js", "pkg": {"scripts": ["main.js", "src/**/*.js"], "assets": ["public/**/*", "node_modules/puppeteer/.local-chromium/**/*", "node_modules/sqlite3/lib/binding/**/*", "node_modules/puppeteer-extra/**/*", "node_modules/puppeteer-extra-plugin-stealth/**/*"], "targets": ["node18-linux-x64"], "outputPath": "dist"}, "scripts": {"start": "electron-forge start", "build": "pkg .", "build:win": "pkg . --targets node18-win-x64 --debug", "build:linux": "pkg . --targets node18-linux-x64", "build:mac": "pkg . --targets node18-macos-x64", "postinstall": "node -e \"require('puppeteer').executablePath()\"", "hunxiao-proto": "rollup -c rollup-hunxiao-proto.config.mjs", "start:websocket-client": "electron ./src/websocket-client-demo.js", "start:remote-server": "node ./src/remote-server-demo.js", "package": "electron-forge package", "make": "electron-forge make --arch x64 --platform win32", "main": "./src/websocket-client-demo.js"}, "dependencies": {"@rollup/plugin-json": "^6.1.0", "@rollup/plugin-terser": "^0.4.4", "body-parser": "^1.20.2", "electron-squirrel-startup": "^1.0.1", "express": "^4.18.2", "express-session": "^1.17.3", "google-protobuf": "^3.21.4", "grpc-tools": "^1.12.4", "jsdom": "^26.1.0", "long": "^5.3.2", "node-abi": "^4.12.0", "protobufjs": "^7.5.1", "protobufjs-cli": "^1.1.3", "puppeteer": "^21.5.0", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "rollup": "^4.40.2", "sqlite3": "^5.1.7"}, "devDependencies": {"@electron-forge/cli": "^7.8.1", "@electron-forge/maker-deb": "^7.8.1", "@electron-forge/maker-rpm": "^7.8.1", "@electron-forge/maker-squirrel": "^7.8.1", "@electron-forge/maker-zip": "^7.8.1", "@electron-forge/plugin-auto-unpack-natives": "^7.8.1", "@electron-forge/plugin-fuses": "^7.8.1", "@electron/fuses": "^1.8.0", "electron": "^37.2.0", "pkg": "^5.8.1"}}