const WebSocket = require('ws');
const crypto = require('crypto');
const {eventCallback} = require("./demo_common");

class RemoteWebSocketServer {
    constructor(port = 8085) {
        this.port = port;
        this.wss = null;
        this.clients = new Map(); // clientId -> {ws, info}
        this.isRunning = false;
    }

// connect f2c16defd371865c https://www.tiktok.com/@speakerlul4/live
// disconnect f2c16defd371865c https://www.tiktok.com/@popmart.usshop/live
    start() {
        if (this.isRunning) {
            console.log('Remote WebSocket server is already running');
            return;
        }

        this.wss = new WebSocket.Server({ port: this.port });
        this.isRunning = true;

        this.wss.on('connection', (ws, req) => {
            const clientId = crypto.randomBytes(8).toString('hex');
            console.log(`New client connected: ${clientId} from ${req.socket.remoteAddress}`);
            
            this.clients.set(clientId, {
                ws: ws,
                info: {
                    id: clientId,
                    connectedAt: new Date().getTime(),
                    context: null,
                    totalConnections: 0
                }
            });

            // 发送欢迎消息
            ws.send(JSON.stringify({
                type: 'welcome',
                message: 'Connected to Remote LiveApi WebSocket Server',
                clientId: clientId,
                timestamp: new Date().getTime()
            }));

            ws.on('message', (message) => {
                try {
                    const data = JSON.parse(message);
                    this.handleClientMessage(clientId, data);
                } catch (error) {
                    console.error('Error parsing client message:', error);
                    ws.send(JSON.stringify({
                        type: 'error',
                        message: 'Invalid JSON format',
                        timestamp: new Date().getTime()
                    }));
                }
            });

            ws.on('close', () => {
                console.log(`Client disconnected: ${clientId}`);
                this.clients.delete(clientId);
            });

            ws.on('error', (error) => {
                console.error(`WebSocket error for client ${clientId}:`, error);
                this.clients.delete(clientId);
            });

            // TODO: remove test,连接发起监听
            this.requestLiveRoom(clientId, "https://www.tiktok.com/@buck_army/live");
        });

        this.wss.on('error', (error) => {
            console.error('WebSocket Server error:', error);
        });

        console.log(`Remote WebSocket server started on port ${this.port}`);
        console.log('Ready to accept LiveWebSocketBridge clients...');
    }

    stop() {
        if (!this.isRunning) {
            console.log('Remote WebSocket server is not running');
            return;
        }

        // 关闭所有客户端连接
        this.clients.forEach(({ws}, clientId) => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.close();
            }
        });
        this.clients.clear();

        // 关闭服务器
        if (this.wss) {
            this.wss.close(() => {
                console.log('Remote WebSocket server stopped');
            });
        }

        this.isRunning = false;
    }

    // 处理客户端消息
    handleClientMessage(clientId, data) {
        const client = this.clients.get(clientId);
        if (!client) return;

        console.log(`Received from client ${clientId}:`, data.type);

        switch (data.type) {
            case 'client_info':
                // 客户端信息
                client.info.context = data.context;
                console.log(`Client ${clientId} context: ${data.context}`);
                break;

            case 'live_events':
                // 直播事件数据
                // console.log(`Live events from ${clientId} (${data.liveUrl}):`, {
                //     connectionId: data.connectionId,
                //     eventCount: data.data?.events_data?.length || 0,
                //     likeTotal: data.data?.like_total || 0,
                //     watchingTotal: data.data?.watching_total || 0
                // });
                try {
                    eventCallback(data.data)

                    data.data.events_data.forEach(event => {
                        switch (event.msg_type) {
                            case 5: {// 直播消息
                                const _control = event.control_msg;
                                switch (_control.action) {
                                    case 0: {
                                        console.log(_control.room_name + ' 正在直播 ' + JSON.stringify(_control));
                                        break
                                    }
                                    case 1: {
                                        console.log('直播结束 ', data.connectionId);
                                        this.requestDisconnectRoom(clientId, data.connectionId);
                                        // finish
                                        break
                                    }
                                    case 100: {
                                        console.log('url错误 ' + _control.live_url + ',' + _control.action_msg);
                                        break
                                    }
                                    case 101: {
                                        console.log('其他错误 ' + _control.live_url + ',' + _control.action_msg);
                                        break
                                    }
                                }
                                break
                            }
                        }
                    });
                } catch (e) {

                }
                // console.log(`Live events from ${clientId} (${data.liveUrl}):`, JSON.stringify(data.data));
                break;

            case 'log':
                // 日志消息
                console.log(`Log from ${clientId}: ${data.message}`);
                break;

            case 'heartbeat':
                // 心跳消息
                client.info.totalConnections = data.totalConnections;
                console.log(`Heartbeat from ${clientId}: ${data.totalConnections} connections`);
                break;

            case 'custom':
                // 自定义消息
                console.log(`Custom message from ${clientId}:`, data.data);
                break;

            case 'error':
                // 错误消息
                console.error(`Error from ${clientId}:`, data.error);
                break;

            case 'client_shutdown':
                // 客户端关闭通知
                console.log(`Client ${clientId} is shutting down`);
                break;
            // case 'disconnect_room_response':
            //     // client.close();
            //     break;
            case 'status_response':
                console.log(`Client ${clientId} is shutting down`);
                break

            default:
                console.log(`Unknown message type from ${clientId}:`, data.type);
        }
    }

    // 发送消息给特定客户端
    sendToClient(clientId, data) {
        const client = this.clients.get(clientId);
        if (!client || client.ws.readyState !== WebSocket.OPEN) {
            console.warn(`Cannot send message to client ${clientId}: not connected`);
            return;
        }

        try {
            client.ws.send(JSON.stringify(data));
        } catch (error) {
            console.error(`Error sending message to client ${clientId}:`, error);
            this.clients.delete(clientId);
        }
    }

    // 广播消息给所有客户端
    broadcast(data) {
        let sentCount = 0;
        this.clients.forEach(({ws}, clientId) => {
            if (ws.readyState === WebSocket.OPEN) {
                try {
                    ws.send(JSON.stringify(data));
                    sentCount++;
                } catch (error) {
                    console.error(`Error broadcasting to client ${clientId}:`, error);
                    this.clients.delete(clientId);
                }
            } else {
                this.clients.delete(clientId);
            }
        });
        console.log(`Broadcasted message to ${sentCount} clients`);
    }

    // 请求客户端连接到直播间
    requestLiveRoom(clientId, liveUrl, options = {}) {
        const requestId = crypto.randomBytes(8).toString('hex');
        
        this.sendToClient(clientId, {
            type: 'live_room_request',
            requestId: requestId,
            liveUrl: liveUrl,
            options: options,
            timestamp: new Date().getTime()
        });

        console.log(`Requested client ${clientId} to connect to live room: ${liveUrl}`);
        return requestId;
    }

    // 请求客户端断开直播间连接
    requestDisconnectRoom(clientId, connectionId = null) {
        const requestId = crypto.randomBytes(8).toString('hex');
        
        this.sendToClient(clientId, {
            type: 'disconnect_room_request',
            requestId: requestId,
            connectionId: connectionId,
            timestamp: new Date().getTime()
        });

        console.log(`Requested client ${clientId} to disconnect from live room: ${connectionId || 'all'}`);
        return requestId;
    }

    // 请求客户端状态
    requestStatus(clientId) {
        const requestId = crypto.randomBytes(8).toString('hex');
        
        this.sendToClient(clientId, {
            type: 'get_status_request',
            requestId: requestId,
            timestamp: new Date().getTime()
        });

        console.log(`Requested status from client ${clientId}`);
        return requestId;
    }

    // 获取所有客户端信息
    getClientsInfo() {
        const clients = [];
        this.clients.forEach(({info}) => {
            clients.push(info);
        });
        return clients;
    }
}

// 如果直接运行此文件，则启动服务器
if (require.main === module) {
    console.log('🚀 启动远程WebSocket服务器...');
    console.log('');

    const server = new RemoteWebSocketServer(8087);
    server.start();

    // 添加交互式命令
    const readline = require('readline');
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });

    console.log('');
    console.log('📋 可用命令:');
    console.log('  clients - 显示所有连接的客户端');
    console.log('  connect <clientId> <liveUrl> - 请求客户端连接到直播间');
    console.log('  disconnect <clientId> [connectionId] - 请求客户端断开连接');
    console.log('  status <clientId> - 请求客户端状态');
    console.log('  broadcast <message> - 广播消息给所有客户端');
    console.log('  quit - 退出服务器');
    console.log('');

    rl.on('line', (input) => {
        const [command, ...args] = input.trim().split(' ');

        switch (command.toLowerCase()) {
            case 'clients':
                const clients = server.getClientsInfo();
                console.log('📋 连接的客户端:');
                clients.forEach(client => {
                    console.log(`  - ${client.id}: ${client.context || 'unknown'} (${client.totalConnections} connections)`);
                });
                break;

            case 'connect':
                if (args.length < 2) {
                    console.log('❌ 用法: connect <clientId> <liveUrl>');
                } else {
                    const [clientId, liveUrl] = args;
                    server.requestLiveRoom(clientId, liveUrl);
                }
                break;

            case 'disconnect':
                if (args.length < 1) { // 停止监听指定直播间，或者所有直播间
                    console.log('❌ 用法: disconnect <clientId> [connectionId]');
                } else {
                    const [clientId, connectionId] = args;
                    server.requestDisconnectRoom(clientId, connectionId);
                }
                break;

            case 'status':
                if (args.length < 1) {
                    console.log('❌ 用法: status <clientId>');
                } else {
                    const [clientId] = args;
                    server.requestStatus(clientId);
                }
                break;

            case 'broadcast':
                if (args.length === 0) {
                    console.log('❌ 用法: broadcast <message>');
                } else {
                    const message = args.join(' ');
                    server.broadcast({
                        type: 'server_broadcast',
                        message: message,
                        timestamp: new Date().getTime()
                    });
                }
                break;

            case 'quit':
                console.log('👋 关闭服务器...');
                server.stop();
                rl.close();
                process.exit(0);
                break;

            default:
                console.log('❓ 未知命令，输入 help 查看可用命令');
        }
    });

    // 处理退出信号
    process.on('SIGINT', () => {
        console.log('\n👋 收到退出信号，关闭服务器...');
        server.stop();
        rl.close();
        process.exit(0);
    });
}

module.exports = RemoteWebSocketServer;
