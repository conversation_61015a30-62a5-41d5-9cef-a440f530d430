function packName(user) {
    var ret = '';
    if (user.fansclub) {
        ret += '[';
        if (user.fansclub.status === false) {
            ret += '*粉丝团已过期*';
        }
        if (user.fansclub.name) {
            ret += user.fansclub.name + ' ';
        }
        ret += 'Club.'+user.fansclub.level+']';
    }
    if (user.level && Number(user.level) !== 0) {
        ret += 'Lv.'+user.level;
    }
    ret += '['/*+user.user_name + ','*/ + user.nick_name+']';
    return ret;
}

function packGiftName(user, to_user) {
    if (to_user) {
        return packName(user) +'给' + packName(to_user);
    } else {
        return packName(user) + '给主播';
    }
}

function getCNTime(eventsData) {
    const east8Date = new Date(eventsData.timestamp);
    return east8Date.toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })+'.'+east8Date.getMilliseconds();
}
function eventCallback(eventsData) {
    if (eventsData.origin !== undefined) {
        // TODO: release remove
        try {
            const fs = require('fs');
            fs.appendFileSync('modules/record/tests/raw/'+eventsData.live_platform_type+'/debug_'+eventsData.live_platform_type+'Events-'+process.pid+'.txt', eventsData.origin+'\n');
        } catch (err) {
            console.error('同步追加出错:', err);
        }
    }

    eventsData.events_data.forEach(event => {
        switch (event.msg_type) {
            case 1: {// 点赞
                console.log(getCNTime(eventsData),packName(event.like_msg.user),'点赞x'+event.like_msg.like_count);
                break;
            }
            case 2: {// 评论
                console.log(getCNTime(eventsData),packName(event.comment_msg.user),'说:',event.comment_msg.content);
                break;
            }
            case 3: {// 送礼物
                const _gift = event.gift_msg;
                if (_gift.batch_size !== 1) {// 一份多少个礼物
                    console.log(getCNTime(eventsData), packGiftName(_gift.user, _gift.to_user) + ' 送出:' + _gift.gift_name
                        + 'x' + _gift.count+',总共:'+_gift.total_count+'*' + _gift.batch_size+'='+_gift.total_count*_gift.batch_size);//+'\t'+_gift.gift_msg_key + ' ,' + _gift.gift_price);
                } else {
                    console.log(getCNTime(eventsData), packGiftName(_gift.user, _gift.to_user) + ' 送出:' + _gift.gift_name
                        + 'x' + _gift.count+',总共:'+_gift.total_count);//+'\t'+_gift.gift_msg_key + ' ,' + _gift.gift_price);
                }
                // {
                //     const _event = event.gift_msg.origin;
                //     if (_event.combo === true) {
                //         // combo 为 true, 最少会收到一次 _event.repeatEnd===0和一次 _event.repeatEnd===1 ，以End结束.
                //         // 需要记录该用户前一次的数量，因为可能会跳过,或者只记录最后一次的总数
                //         if (_event.repeatEnd === 1) {
                //             // 这里可以记录礼物总连击数: _event.groupCount*_event.comboCount
                //             if (_event.comboCount > 1) {
                //                 console.log(packName(_gift.user) + ' 一共送了:' + _event.giftName + ' x'+_event.groupCount*_event.comboCount + ' (连击结束)')
                //             }
                //         } else {
                //             // 只用来显示，因为礼物数据不一定是顺序增加的
                //             console.log(packName(_gift.user) + ' 送了:' + _event.giftName + ' x'+_event.groupCount*_event.comboCount + (_event.comboCount > 1 ? (' (连击中)') : ''))
                //         }
                //     } else {
                //         // 当前消息礼物数: _event.groupCount
                //         console.log(packName(_gift.user) + ' 送了:' + _event.giftName + ' x'+_event.groupCount + ' (连击x'+_event.groupCount*_event.comboCount+')')
                //     }
                // }
                break;
            }
            case 4: {// 进人
                console.log(getCNTime(eventsData),packName(event.member_msg.user),'来了');
                break
            }
            case 5: {// 直播消息
                const _control = event.control_msg;
                switch (_control.action) {
                    case 0: {
                        console.log(getCNTime(eventsData),_control.room_name+' 正在直播 '+JSON.stringify(_control));
                        break
                    }
                    case 1: {
                        console.log(getCNTime(eventsData),'直播结束');
                        // finish
                        break
                    }
                    case 100: {
                        console.log(getCNTime(eventsData),'url错误 '+_control.live_url+','+_control.action_msg);
                        break
                    }
                    case 101: {
                        console.log(getCNTime(eventsData),'其他错误 '+_control.live_url+','+_control.action_msg);
                        break
                    }
                    // 快手错误信息
                    case 1000: {
                        console.log(getCNTime(eventsData),'还未开始直播',_control.action_msg);
                        break
                    }
                    case 1001: {
                        console.log(getCNTime(eventsData),'请求过快，请稍后重试。可能需要登录快手账号，或者重启路由器更换ip地址。'+_control.action_msg);
                        break
                    }
                    case 1002: {
                        console.log(getCNTime(eventsData),'没有找到直播间名,'+_control.action_msg);
                        break
                    }
                    default: {
                        console.log(getCNTime(eventsData),'未知action:'+_control.action);
                        break
                    }
                }
                break
            }
        }
    })
}

module.exports = {
    eventCallback: eventCallback
}